class Arena {
    constructor(w,h) {
        const matrix = [];
        while (h--){
            matrix.push(new Array(w).fill(0));
        }
        this.matrix = matrix;
    }

    

    Collide(player){
        const [m, o] = [player.matrix, player.pos];
        for (let y = 0; y < m.length; ++y){
            for (let x = 0; x < m[y].length; ++x){
                if (m[y][x] !== 0 && (this.matrix[y + o.y] && this.matrix[y + o.y][x +o.x]) !== 0){
                    return true;
                }
            }
        }
        return false;
    }

    Merge(player){
        player.matrix.forEach((row, y) =>{
            row.forEach((value, x) =>{
                if (value !== 0){
                    this.matrix[y + player.pos.y][x + player.pos.x] = value;
                }
            });
        });
    }

    Sweep(){
        let rowCount = 1;
        outer: for (let y = this.length - 1; y > 0; --y){
            for (let x = 0; x < this[y].length; ++x){
                if (this[y][x] === 0){
                    continue outer;
                }
            }

            const row = this.splice(y, 1)[0].fill(0);
            this.unshift(row);
            ++y;

            player.score += rowCount * 10;
            rowCount *= 2;
        }
    }

    Clear() {
        this.matrix.forEach(row => row.fill(0));
    }
}