class Tetris {
    constructor(canvas) {
        this.canvas = canvas;
        this.context = canvas.getContext('2d');
        this.context.scale(20, 20);

        this.arena = new Arena(12, 20);
        this.player = new Player(this);

        this.colors = [
            null,   //0
            '#D810E3',  //1 - T - purple
            '#FFFC3D',  //2 - O - yellow
            '#0033FF',  //3 - L - blue
            '#FF0000',  //4 - J - red
            '#00EAFF',  //5 - I - cyan
            '#FF8E0D',  //6 - S - orange
            '#00B350',  //7 - Z - green
        ];

        let lastTime = 0;
        const Update = (time = 0) =>{
            const deltaTime = time - lastTime;
            lastTime = time;

            this.player.Update(deltaTime);

            this.Draw();
            requestAnimationFrame(Update);
        }

        Update();
    }

    // Create the Arena
    Draw(){
        this.context.fillStyle = '#000000';
        this.context.fillRect(0, 0, this.canvas.width, this.canvas.height);

        this.drawMatrix(this.arena.matrix, {x:0, y:0})
        this.drawMatrix(this.player.matrix, this.player.pos);
    }

    drawMatrix(matrix, offset){
        matrix.forEach((row, y) => {
            row.forEach((value, x) => {
                if (value !== 0){
                    this.context.fillStyle = this.colors[value];
                    this.context.fillRect(x + offset.x,
                                    y + offset.y,
                                    1, 1);
                }
            });
        });
    }
}