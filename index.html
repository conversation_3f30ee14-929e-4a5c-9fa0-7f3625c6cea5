<html>
    <head>
        <title><PERSON><PERSON>s</title>
        <style>
            body{
                background-color: #202028;
                display: flex;
                color: #ffffff;
                font-family: sans-serif;
                font-size: 2em;
                text-align: center;
            }

            .player{
                flex: 1 1 auto;
            }

            canvas{
                border: solid .1em #ffffff;
                height: 90vh;
            }

            .menu {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(32, 32, 40, 0.95);
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                z-index: 1000;
            }

            .menu button {
                margin: 10px;
                padding: 15px 30px;
                font-size: 1.2em;
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 5px;
                cursor: pointer;
            }

            .menu button:hover {
                background-color: #45a049;
            }

            .menu input {
                margin: 10px;
                padding: 10px;
                font-size: 1em;
                border: 1px solid #ccc;
                border-radius: 5px;
            }

            #room-info {
                position: absolute;
                top: 10px;
                right: 10px;
                background-color: rgba(0, 0, 0, 0.7);
                padding: 10px;
                border-radius: 5px;
                display: none;
            }

            .hidden {
                display: none !important;
            }
        </style>
    </head>
    <body>
        <div id="menu" class="menu">
            <h1>Multiplayer Tetris</h1>
            <button onclick="startSinglePlayer()">Single Player</button>
            <button onclick="showMultiplayerOptions()">Multiplayer</button>

            <div id="multiplayer-options" class="hidden">
                <button onclick="createRoom()">Create Room</button>
                <div>
                    <input type="text" id="room-id-input" placeholder="Enter Room ID">
                    <button onclick="joinRoom()">Join Room</button>
                </div>
                <button onclick="showMainMenu()">Back</button>
            </div>
        </div>

        <div id="room-info"></div>

        <div id="game-area" class="hidden">
            <div class="player" data-player="0">
                <div class="score">Score: 0</div>
                <canvas width="240" height="400"></canvas>
            </div>

            <div class="player" data-player="1">
                <div class="score">Score: 0</div>
                <canvas width="240" height="400"></canvas>
            </div>
        </div>

        <script src="arena.js"></script>
        <script src="player.js"></script>
        <script src="tetris.js"></script>
        <script src="websocket-client.js"></script>
        <script src="main.js"></script>
    </body>
</html>