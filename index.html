<html>
    <head>
        <title>Tetris</title>
        <style>
            body{
                background-color: #202028;
                color: #ffffff;
                font-family: sans-serif;
                font-size: 2em;
                text-align: center;
                margin: 0;
                padding: 0;
            }

            #game-area {
                display: flex;
                justify-content: center;
                align-items: flex-start;
                height: 100vh;
                gap: 20px;
                padding: 20px;
            }

            .player{
                flex: 0 0 auto;
                display: flex;
                flex-direction: column;
                align-items: center;
                margin: 0 10px;
                padding: 10px;
                border-radius: 10px;
                background-color: rgba(255, 255, 255, 0.05);
            }

            .player[data-player="0"] .score::before {
                content: "Player 1 - ";
            }

            .player[data-player="1"] .score::before {
                content: "Player 2 - ";
            }

            canvas{
                border: solid .1em #ffffff;
                height: 80vh;
                width: auto;
                max-width: 300px;
            }

            .score {
                margin-bottom: 10px;
                font-size: 0.8em;
                font-weight: bold;
            }

            .menu {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(32, 32, 40, 0.95);
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                z-index: 1000;
            }

            .menu button {
                margin: 10px;
                padding: 15px 30px;
                font-size: 1.2em;
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 5px;
                cursor: pointer;
            }

            .menu button:hover {
                background-color: #45a049;
                transform: translateY(-2px);
                box-shadow: 0 4px 8px rgba(0,0,0,0.3);
            }

            .menu input {
                margin: 10px;
                padding: 10px;
                font-size: 1em;
                border: 1px solid #ccc;
                border-radius: 5px;
                background-color: rgba(255,255,255,0.9);
                color: #333;
            }

            .menu input:focus {
                outline: none;
                border-color: #4CAF50;
                box-shadow: 0 0 5px rgba(76, 175, 80, 0.5);
            }

            .menu h1 {
                margin-bottom: 10px;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            }

            .menu h2 {
                color: #4CAF50;
                margin-bottom: 15px;
            }

            #main-menu-options {
                margin-bottom: 20px;
            }

            #room-info {
                position: absolute;
                top: 10px;
                right: 10px;
                background-color: rgba(0, 0, 0, 0.7);
                padding: 10px;
                border-radius: 5px;
                display: none;
            }

            #game-controls {
                position: absolute;
                top: 10px;
                left: 10px;
                display: flex;
                flex-direction: column;
                gap: 10px;
            }

            #return-home-btn {
                padding: 8px 16px;
                font-size: 0.6em;
                background-color: #ff4444;
                color: white;
                border: none;
                border-radius: 5px;
                cursor: pointer;
                transition: background-color 0.3s;
            }

            #return-home-btn:hover {
                background-color: #cc3333;
            }

            #pause-btn {
                padding: 8px 16px;
                font-size: 0.6em;
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 5px;
                cursor: pointer;
                transition: background-color 0.3s;
            }

            #pause-btn:hover {
                background-color: #45a049;
            }

            .hidden {
                display: none !important;
            }

            #game-over-screen {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.8);
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                z-index: 2000;
            }

            #game-over-screen h2 {
                color: #ff4444;
                font-size: 2em;
                margin-bottom: 20px;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            }

            #game-over-screen p {
                font-size: 1.2em;
                margin-bottom: 30px;
                color: #fff;
            }
        </style>
    </head>
    <body>
        <div id="menu" class="menu">
            <h1>🎮 Multiplayer Tetris</h1>
            <p style="font-size: 0.5em; margin-bottom: 30px; color: #ccc;">
                Use arrow keys to play • ESC to pause • Enjoy the game!
            </p>

            <div id="main-menu-options">
                <button onclick="startSinglePlayer()">🎯 Single Player</button>
                <button onclick="showMultiplayerOptions()">👥 Multiplayer</button>
            </div>

            <div id="multiplayer-options" class="hidden">
                <h2 style="font-size: 0.8em; margin-bottom: 20px;">Multiplayer Options</h2>
                <button onclick="createRoom()">🏠 Create Room</button>
                <div style="margin: 15px 0;">
                    <input type="text" id="room-id-input" placeholder="Enter Room ID (e.g., ABC123)">
                    <button onclick="joinRoom()">🚪 Join Room</button>
                </div>
                <button onclick="showMainMenu()" style="background-color: #666;">⬅️ Back</button>

                <div style="font-size: 0.4em; margin-top: 20px; color: #999; max-width: 400px;">
                    <p><strong>How to play multiplayer:</strong></p>
                    <p>1. One player creates a room and shares the Room ID</p>
                    <p>2. Other player enters the Room ID and joins</p>
                    <p>3. Both players can see each other's game in real-time!</p>
                </div>
            </div>
        </div>

        <div id="game-over-screen" class="hidden">
            <h2>🎮 Game Over!</h2>
            <p id="final-score">Final Score: 0</p>
            <button onclick="restartGame()" style="margin: 10px; padding: 15px 30px; font-size: 1.2em; background-color: #4CAF50; color: white; border: none; border-radius: 5px; cursor: pointer;">🔄 Play Again</button>
            <button onclick="returnToHome()" style="margin: 10px; padding: 15px 30px; font-size: 1.2em; background-color: #666; color: white; border: none; border-radius: 5px; cursor: pointer;">🏠 Main Menu</button>
        </div>

        <div id="room-info"></div>

        <div id="game-controls" class="hidden">
            <button id="return-home-btn" onclick="returnToHome()">🏠 Return Home</button>
            <button id="pause-btn" onclick="togglePause()">⏸️ Pause</button>
            <div style="font-size: 0.4em; color: #ccc; margin-top: 10px; text-align: left;">
                <div>⌨️ Controls:</div>
                <div>← → ↑ ↓ Arrow Keys</div>
                <div>ESC: Pause/Resume</div>
            </div>
        </div>

        <div id="game-area" class="hidden">
            <div class="player" data-player="0">
                <div class="score">Score: 0</div>
                <canvas width="240" height="400"></canvas>
            </div>

            <div class="player" data-player="1">
                <div class="score">Score: 0</div>
                <canvas width="240" height="400"></canvas>
            </div>
        </div>

        <script src="arena.js"></script>
        <script src="player.js"></script>
        <script src="tetris.js"></script>
        <script src="websocket-client.js"></script>
        <script src="main.js"></script>
    </body>
</html>