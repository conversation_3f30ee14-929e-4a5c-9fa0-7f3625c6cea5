const express = require('express');
const WebSocket = require('ws');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3000;

// Serve static files
app.use(express.static(path.join(__dirname)));

// Start HTTP server
const server = app.listen(PORT, () => {
    console.log(`Server running on http://localhost:${PORT}`);
});

// Create WebSocket server
const wss = new WebSocket.Server({ server });

// Game rooms storage
const gameRooms = new Map();

class GameRoom {
    constructor(id) {
        this.id = id;
        this.players = [];
        this.gameState = {
            started: false,
            gameData: {}
        };
    }

    addPlayer(ws, playerId) {
        if (this.players.length >= 2) {
            return false; // Room is full
        }
        
        const player = {
            id: playerId,
            ws: ws,
            ready: false,
            gameData: null
        };
        
        this.players.push(player);
        ws.roomId = this.id;
        ws.playerId = playerId;
        
        return true;
    }

    removePlayer(playerId) {
        this.players = this.players.filter(p => p.id !== playerId);
        if (this.players.length === 0) {
            gameRooms.delete(this.id);
        }
    }

    broadcast(message, excludePlayerId = null) {
        this.players.forEach(player => {
            if (player.id !== excludePlayerId && player.ws.readyState === WebSocket.OPEN) {
                player.ws.send(JSON.stringify(message));
            }
        });
    }

    getPlayerById(playerId) {
        return this.players.find(p => p.id === playerId);
    }
}

// WebSocket connection handling
wss.on('connection', (ws) => {
    console.log('New client connected');

    ws.on('message', (data) => {
        try {
            const message = JSON.parse(data);
            handleMessage(ws, message);
        } catch (error) {
            console.error('Error parsing message:', error);
        }
    });

    ws.on('close', () => {
        console.log('Client disconnected');
        if (ws.roomId && ws.playerId) {
            const room = gameRooms.get(ws.roomId);
            if (room) {
                room.removePlayer(ws.playerId);
                room.broadcast({
                    type: 'playerLeft',
                    playerId: ws.playerId
                });
            }
        }
    });
});

function handleMessage(ws, message) {
    switch (message.type) {
        case 'createRoom':
            createRoom(ws, message);
            break;
        case 'joinRoom':
            joinRoom(ws, message);
            break;
        case 'gameUpdate':
            handleGameUpdate(ws, message);
            break;
        case 'playerReady':
            handlePlayerReady(ws, message);
            break;
        case 'startGame':
            handleStartGame(ws, message);
            break;
        default:
            console.log('Unknown message type:', message.type);
    }
}

function createRoom(ws, message) {
    const roomId = generateRoomId();
    const room = new GameRoom(roomId);
    gameRooms.set(roomId, room);
    
    const success = room.addPlayer(ws, message.playerId);
    
    ws.send(JSON.stringify({
        type: 'roomCreated',
        roomId: roomId,
        success: success
    }));
}

function joinRoom(ws, message) {
    const room = gameRooms.get(message.roomId);
    
    if (!room) {
        ws.send(JSON.stringify({
            type: 'joinRoomResponse',
            success: false,
            error: 'Room not found'
        }));
        return;
    }
    
    const success = room.addPlayer(ws, message.playerId);
    
    ws.send(JSON.stringify({
        type: 'joinRoomResponse',
        success: success,
        roomId: message.roomId,
        playerCount: room.players.length
    }));
    
    if (success) {
        room.broadcast({
            type: 'playerJoined',
            playerId: message.playerId,
            playerCount: room.players.length
        }, message.playerId);
    }
}

function handleGameUpdate(ws, message) {
    const room = gameRooms.get(ws.roomId);
    if (!room) return;
    
    const player = room.getPlayerById(ws.playerId);
    if (player) {
        player.gameData = message.gameData;
        
        room.broadcast({
            type: 'gameUpdate',
            playerId: ws.playerId,
            gameData: message.gameData
        }, ws.playerId);
    }
}

function handlePlayerReady(ws, message) {
    const room = gameRooms.get(ws.roomId);
    if (!room) return;
    
    const player = room.getPlayerById(ws.playerId);
    if (player) {
        player.ready = message.ready;
        
        room.broadcast({
            type: 'playerReady',
            playerId: ws.playerId,
            ready: message.ready
        });
        
        // Check if all players are ready
        const allReady = room.players.length === 2 && room.players.every(p => p.ready);
        if (allReady) {
            room.broadcast({
                type: 'allPlayersReady'
            });
        }
    }
}

function handleStartGame(ws, message) {
    const room = gameRooms.get(ws.roomId);
    if (!room) return;
    
    room.gameState.started = true;
    room.broadcast({
        type: 'gameStarted'
    });
}

function generateRoomId() {
    return Math.random().toString(36).substring(2, 8).toUpperCase();
}

console.log('WebSocket server is running...');
