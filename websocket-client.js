class WebSocketClient {
    constructor() {
        this.ws = null;
        this.connected = false;
        this.roomId = null;
        this.playerId = this.generatePlayerId();
        this.isHost = false;
        this.gameMode = 'single'; // 'single' or 'multi'
        this.callbacks = {};
    }

    generatePlayerId() {
        return 'player_' + Math.random().toString(36).substring(2, 9);
    }

    connect() {
        return new Promise((resolve, reject) => {
            try {
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const wsUrl = `${protocol}//${window.location.host}`;
                
                this.ws = new WebSocket(wsUrl);

                this.ws.onopen = () => {
                    console.log('Connected to WebSocket server');
                    this.connected = true;
                    resolve();
                };

                this.ws.onmessage = (event) => {
                    try {
                        const message = JSON.parse(event.data);
                        this.handleMessage(message);
                    } catch (error) {
                        console.error('Error parsing WebSocket message:', error);
                    }
                };

                this.ws.onclose = () => {
                    console.log('Disconnected from WebSocket server');
                    this.connected = false;
                    this.roomId = null;
                };

                this.ws.onerror = (error) => {
                    console.error('WebSocket error:', error);
                    reject(error);
                };

            } catch (error) {
                reject(error);
            }
        });
    }

    handleMessage(message) {
        console.log('Received message:', message);
        
        switch (message.type) {
            case 'roomCreated':
                this.handleRoomCreated(message);
                break;
            case 'joinRoomResponse':
                this.handleJoinRoomResponse(message);
                break;
            case 'playerJoined':
                this.handlePlayerJoined(message);
                break;
            case 'playerLeft':
                this.handlePlayerLeft(message);
                break;
            case 'gameUpdate':
                this.handleGameUpdate(message);
                break;
            case 'playerReady':
                this.handlePlayerReady(message);
                break;
            case 'allPlayersReady':
                this.handleAllPlayersReady(message);
                break;
            case 'gameStarted':
                this.handleGameStarted(message);
                break;
        }

        // Call registered callbacks
        if (this.callbacks[message.type]) {
            this.callbacks[message.type](message);
        }
    }

    on(eventType, callback) {
        this.callbacks[eventType] = callback;
    }

    createRoom() {
        if (!this.connected) return;
        
        this.isHost = true;
        this.send({
            type: 'createRoom',
            playerId: this.playerId
        });
    }

    joinRoom(roomId) {
        if (!this.connected) return;
        
        this.send({
            type: 'joinRoom',
            roomId: roomId,
            playerId: this.playerId
        });
    }

    sendGameUpdate(gameData) {
        if (!this.connected || !this.roomId) return;
        
        this.send({
            type: 'gameUpdate',
            gameData: gameData
        });
    }

    setPlayerReady(ready) {
        if (!this.connected || !this.roomId) return;
        
        this.send({
            type: 'playerReady',
            ready: ready
        });
    }

    startGame() {
        if (!this.connected || !this.roomId || !this.isHost) return;
        
        this.send({
            type: 'startGame'
        });
    }

    send(message) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify(message));
        }
    }

    handleRoomCreated(message) {
        if (message.success) {
            this.roomId = message.roomId;
            console.log('Room created:', this.roomId);
            this.updateUI();
        }
    }

    handleJoinRoomResponse(message) {
        if (message.success) {
            this.roomId = message.roomId;
            console.log('Joined room:', this.roomId);
            this.updateUI();
        } else {
            alert('Failed to join room: ' + (message.error || 'Unknown error'));
        }
    }

    handlePlayerJoined(message) {
        console.log('Player joined:', message.playerId);
        this.updateUI();
    }

    handlePlayerLeft(message) {
        console.log('Player left:', message.playerId);
        this.updateUI();
    }

    handleGameUpdate(message) {
        // Update opponent's game state
        if (message.playerId !== this.playerId) {
            this.updateOpponentGame(message.gameData);
        }
    }

    handlePlayerReady(message) {
        console.log('Player ready status:', message.playerId, message.ready);
        this.updateUI();
    }

    handleAllPlayersReady(message) {
        console.log('All players ready!');
        this.updateUI();
    }

    handleGameStarted(message) {
        console.log('Game started!');
        this.gameMode = 'multi';
        this.updateUI();
    }

    updateOpponentGame(gameData) {
        // Update the opponent's game display
        if (gameData && tetri.length > 1) {
            const opponentIndex = this.getOpponentIndex();
            if (tetri[opponentIndex] && gameData.arena) {
                // Update opponent's arena
                tetri[opponentIndex].arena.matrix = gameData.arena.map(row => [...row]);

                // Update opponent's current piece
                if (gameData.player) {
                    if (gameData.player.matrix) {
                        tetri[opponentIndex].player.matrix = gameData.player.matrix.map(row => [...row]);
                    }
                    if (gameData.player.pos) {
                        tetri[opponentIndex].player.pos = { ...gameData.player.pos };
                    }
                    if (typeof gameData.player.score === 'number') {
                        tetri[opponentIndex].player.score = gameData.player.score;
                    }
                }

                // Update score display
                updateScore();
            }
        }
    }

    getOpponentIndex() {
        // Assuming player 0 is local player, opponent is player 1
        return 1;
    }

    updateUI() {
        // Update room info display
        const roomInfo = document.getElementById('room-info');
        if (roomInfo) {
            if (this.roomId) {
                roomInfo.textContent = `Room: ${this.roomId}`;
                roomInfo.style.display = 'block';
            } else {
                roomInfo.style.display = 'none';
            }
        }
    }

    disconnect() {
        if (this.ws) {
            this.ws.close();
        }
    }

    getGameData() {
        if (tetri.length > 0) {
            const localPlayer = tetri[0];
            return {
                arena: localPlayer.arena.matrix,
                player: {
                    matrix: localPlayer.player.matrix,
                    pos: localPlayer.player.pos,
                    score: localPlayer.player.score
                }
            };
        }
        return null;
    }
}

// Global WebSocket client instance
let wsClient = null;
