function createPiece(type){
    if (type === 'T'){
        return matrix = [
            [0, 0, 0],
            [1, 1, 1],
            [0, 1, 0],
        ];
    } else if (type === 'O'){
        return matrix = [
            [2, 2],
            [2, 2],
        ];
    } else if (type === 'L'){
        return matrix = [
            [0, 3, 0],
            [0, 3, 0],
            [0, 3, 3],
        ];
    } else if (type === 'J'){
        return matrix = [
            [0, 4, 0],
            [0, 4, 0],
            [4, 4, 0],
        ];
    } else if (type === 'I'){
        return matrix = [
            [0, 5, 0, 0],
            [0, 5, 0, 0],
            [0, 5, 0, 0],
            [0, 5, 0, 0],
        ];
    } else if (type === 'S'){
        return matrix = [
            [0, 6, 6],
            [6, 6, 0],
            [0, 0, 0],
        ];
    } else if (type === 'Z'){
        return matrix = [
            [7, 7, 0],
            [0, 7, 7],
            [0, 0, 0],
        ];
    }
}

function updateScore(){
    // document.getElementById('score').innerText = 'Score: ' + tetris.player.score; // single player
    document.getElementById('score').innerText = 'Score: ' + tetri[0].player.score; // multi player
}

const tetri = [];

// Inital Setting
const playerElements =  document.querySelectorAll('.player');
[...playerElements].forEach(element => {
    
    const canvas = element.querySelector('canvas');
    const tetris = new Tetris(canvas);
    tetri.push(tetris);

});

// single player // 
// const canvas = document.getElementById('tetris');
// const tetris = new Tetris(canvas);


// document.addEventListener('keydown', event => {
//     const player = tetris.player;
//     // console.log(event);
//     if (event.keyCode === 37){ //left
//         player.Move(-1);
//         // player.pos.x--;
//     } else if (event.keyCode === 39){ //right
//         player.Move(1);
//         // player.pos.x++;
//     } else if (event.keyCode === 38){ //upkey
//         // player.pos.y--;
//         player.Rotate(1);
//     } else if (event.keyCode === 40){ //downkey
//         // player.pos.y++;
//         player.Drop();
//     }
// });

// //Start the Game
// updateScore();

//multiplayer
document.addEventListener('keydown', event => {
    const player = tetri[0].player;
    // console.log(event);
    if (event.keyCode === 37){ //left
        player.Move(-1);
        // player.pos.x--;
    } else if (event.keyCode === 39){ //right
        player.Move(1);
        // player.pos.x++;
    } else if (event.keyCode === 38){ //upkey
        // player.pos.y--;
        player.Rotate(1);
    } else if (event.keyCode === 40){ //downkey
        // player.pos.y++;
        player.Drop();
    }
});

//Start the Game
updateScore();