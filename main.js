function createPiece(type){
    if (type === 'T'){
        return matrix = [
            [0, 0, 0],
            [1, 1, 1],
            [0, 1, 0],
        ];
    } else if (type === 'O'){
        return matrix = [
            [2, 2],
            [2, 2],
        ];
    } else if (type === 'L'){
        return matrix = [
            [0, 3, 0],
            [0, 3, 0],
            [0, 3, 3],
        ];
    } else if (type === 'J'){
        return matrix = [
            [0, 4, 0],
            [0, 4, 0],
            [4, 4, 0],
        ];
    } else if (type === 'I'){
        return matrix = [
            [0, 5, 0, 0],
            [0, 5, 0, 0],
            [0, 5, 0, 0],
            [0, 5, 0, 0],
        ];
    } else if (type === 'S'){
        return matrix = [
            [0, 6, 6],
            [6, 6, 0],
            [0, 0, 0],
        ];
    } else if (type === 'Z'){
        return matrix = [
            [7, 7, 0],
            [0, 7, 7],
            [0, 0, 0],
        ];
    }
}

function updateScore(){
    if (!gameInitialized || tetri.length === 0) return;

    tetri.forEach((tetris, index) => {
        const playerElement = document.querySelector(`[data-player="${index}"]`);
        if (playerElement && tetris && tetris.player) {
            const scoreElement = playerElement.querySelector('.score');
            if (scoreElement) {
                scoreElement.innerText = 'Score: ' + tetris.player.score;
            }
        }
    });
}

const tetri = [];
let gameMode = 'single'; // 'single' or 'multi'
let gameInitialized = false;
let gamePaused = false;

function initializeGame(mode = 'single') {
    gameMode = mode;

    // Clear existing game instances
    tetri.length = 0;

    // Initialize game instances
    const playerElements = document.querySelectorAll('.player');
    [...playerElements].forEach((element, index) => {
        const canvas = element.querySelector('canvas');
        const tetris = new Tetris(canvas);
        tetri.push(tetris);

        // In single player mode, hide the second player
        if (mode === 'single' && index === 1) {
            element.style.display = 'none';
        } else {
            element.style.display = 'flex';
        }
    });

    // Adjust game area layout based on mode
    const gameArea = document.getElementById('game-area');
    if (mode === 'single') {
        gameArea.style.justifyContent = 'center';
    } else {
        gameArea.style.justifyContent = 'center';
    }

    gameInitialized = true;
    updateScore();
}

// Removed old single player code - now handled by initializeGame function

// Game controls
document.addEventListener('keydown', event => {
    if (!gameInitialized || tetri.length === 0 || gamePaused) return;

    const player = tetri[0].player; // Local player is always index 0

    if (event.key === 'ArrowLeft'){ //left
        player.Move(-1);
    } else if (event.key === 'ArrowRight'){ //right
        player.Move(1);
    } else if (event.key === 'ArrowUp'){ //upkey
        player.Rotate(1);
    } else if (event.key === 'ArrowDown'){ //downkey
        player.Drop();
    } else if (event.key === 'Escape'){ // ESC key to pause
        togglePause();
    }
});

// Menu functions
function startSinglePlayer() {
    hideMenu();
    showGameArea();
    initializeGame('single');
}

function showMultiplayerOptions() {
    document.getElementById('multiplayer-options').classList.remove('hidden');
}

function showMainMenu() {
    document.getElementById('multiplayer-options').classList.add('hidden');
}

function hideMenu() {
    document.getElementById('menu').classList.add('hidden');
}

function showGameArea() {
    document.getElementById('game-area').classList.remove('hidden');
    document.getElementById('game-controls').classList.remove('hidden');
}

function hideGameArea() {
    document.getElementById('game-area').classList.add('hidden');
    document.getElementById('game-controls').classList.add('hidden');
}

function showMenu() {
    document.getElementById('menu').classList.remove('hidden');
}

function returnToHome() {
    // Confirm before returning to home
    if (confirm('Are you sure you want to return to the main menu? Your current game will be lost.')) {
        // Stop the current game
        stopGame();

        // Disconnect from WebSocket if connected
        if (wsClient && wsClient.connected) {
            wsClient.disconnect();
            wsClient = null;
        }

        // Reset game state
        gameInitialized = false;
        gamePaused = false;
        tetri.length = 0;

        // Hide game area and show menu
        hideGameArea();
        showMenu();

        // Reset multiplayer options
        document.getElementById('multiplayer-options').classList.add('hidden');
        document.getElementById('room-info').style.display = 'none';
        document.getElementById('room-id-input').value = '';
    }
}

function togglePause() {
    gamePaused = !gamePaused;
    const pauseBtn = document.getElementById('pause-btn');

    if (gamePaused) {
        pauseBtn.textContent = '▶️ Resume';
        pauseBtn.style.backgroundColor = '#ff9800';
        // Pause all game instances
        tetri.forEach(tetris => {
            if (tetris.player) {
                tetris.player.dropInterval = Infinity; // Effectively pause
            }
        });
    } else {
        pauseBtn.textContent = '⏸️ Pause';
        pauseBtn.style.backgroundColor = '#4CAF50';
        // Resume all game instances
        tetri.forEach(tetris => {
            if (tetris.player) {
                tetris.player.dropInterval = 1000; // Resume normal speed
            }
        });
    }
}

function stopGame() {
    // Stop all game loops and reset
    tetri.forEach(tetris => {
        if (tetris.player) {
            tetris.player.dropInterval = Infinity;
        }
    });
}

function showGameOver(finalScore = 0) {
    document.getElementById('final-score').textContent = `Final Score: ${finalScore}`;
    document.getElementById('game-over-screen').classList.remove('hidden');
    gamePaused = true;
}

function hideGameOver() {
    document.getElementById('game-over-screen').classList.add('hidden');
}

function restartGame() {
    hideGameOver();

    if (gameMode === 'single') {
        // Restart single player
        initializeGame('single');
    } else {
        // For multiplayer, we might want to restart or return to menu
        // For now, let's return to menu for multiplayer
        returnToHome();
    }
}

async function createRoom() {
    try {
        if (!wsClient) {
            wsClient = new WebSocketClient();
            await wsClient.connect();
        }

        wsClient.createRoom();
        wsClient.on('roomCreated', (message) => {
            if (message.success) {
                hideMenu();
                showGameArea();
                initializeGame('multi');
                initializeWebSocketCallbacks();
                alert(`Room created! Room ID: ${message.roomId}\nShare this ID with your friend.`);
            }
        });

    } catch (error) {
        console.error('Failed to create room:', error);
        alert('Failed to connect to server');
    }
}

async function joinRoom() {
    const roomId = document.getElementById('room-id-input').value.trim();
    if (!roomId) {
        alert('Please enter a room ID');
        return;
    }

    try {
        if (!wsClient) {
            wsClient = new WebSocketClient();
            await wsClient.connect();
        }

        wsClient.joinRoom(roomId);
        wsClient.on('joinRoomResponse', (message) => {
            if (message.success) {
                hideMenu();
                showGameArea();
                initializeGame('multi');
                initializeWebSocketCallbacks();
            }
        });

    } catch (error) {
        console.error('Failed to join room:', error);
        alert('Failed to connect to server');
    }
}

// WebSocket game synchronization
function sendGameUpdate() {
    if (wsClient && wsClient.connected && gameMode === 'multi') {
        const gameData = wsClient.getGameData();
        if (gameData) {
            wsClient.sendGameUpdate(gameData);
        }
    }
}

// Initialize WebSocket client callbacks
function initializeWebSocketCallbacks() {
    if (wsClient) {
        wsClient.on('gameUpdate', (message) => {
            // Handle opponent's game update
            if (message.playerId !== wsClient.playerId) {
                wsClient.updateOpponentGame(message.gameData);
            }
        });
    }
}