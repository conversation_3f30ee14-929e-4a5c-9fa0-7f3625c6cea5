<html>
    <head>
        <title>Tetris</title>
        <style>
            body{
                background-color: #202028;
                display: flex;
                color: #ffffff;
                font-family: sans-serif;
                font-size: 2em;
                text-align: center;
            }

            .player{
                flex: 1 1 auto;
            }
            
            canvas{
                border: solid .1em #ffffff;
                height: 90vh;
            }
        </style>
    </head>
    <body>
        <div class="player">
            <div id ="score"></div>
            <canvas id="tetris" width="240" height="400"></canvas>
        </div>
        
        <div class="player">
            <div id ="score"></div>
            <canvas id="tetris" width="240" height="400"></canvas>
        </div>
        
        <script src="arena.js"></script>
        <script src="player.js"></script>
        <script src="tetris.js"></script>
        <script src="main.js"></script>
    </body>
</html>